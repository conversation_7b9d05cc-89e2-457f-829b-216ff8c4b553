<template>
  <view class="container">
    <!-- 直接显示商品内容，无需权限检查 -->
    <view class="content">
      <view class="header">
        <view class="search-container">
          <view class="custom-search-bar">
            <view class="search-input-wrapper">
              <uni-icons type="search" size="16" color="#999" class="search-icon"></uni-icons>
              <input class="search-input" type="text" placeholder="请输入关键字" v-model="searchKeyword" @confirm="search"
                @focus="goToSearch" />
            </view>
          </view>
          <view class="search-button" @click="handleSearch">
            <button>搜索</button>
          </view>
        </view>
        <!-- 标题栏 -->
        <view class="tabs">
          <view v-for="(tab, index) in tabs" :key="index" class="tab-item" :class="{ active: activeTab === index }"
            @click="switchTab(index)">
            {{ tab.title }}

            <!-- 右侧箭头操作区 -->
            <!-- <view class="arrow-container" v-if="activeTab === 1 && index === activeTab">
						
						<text class="arrow" :class="{ active: sortOrder === 'asc' }">
							▲
						</text>

						
						<text class="arrow" :class="{ active: sortOrder === 'desc' }" style="  
				display: inline-block;
				transform: rotateX(180deg);">
							▲
						</text>
					</view> -->
          </view>
        </view>
      </view>

      <!-- 内容区域 -->
      <!-- 综合 -->
      <scroll-view scroll-y class="content-scroll" v-if="activeTab === 0">
        <view class="product-section" v-for="(product, index) in products" :key="index"
          @click="goToDetails(product.id)">
          <!-- 商品图片 -->
          <image class="product-image" :src="getFirstImage(product.goodsCover)" mode="widthFix" />

          <!-- 商品信息 -->
          <view class="product-info">
            <text class="product-title">{{ product.goodsName }}</text>

            <view class="product-bought">
              <!-- 价格标签 -->
              <view class="price-tag">
                <text class="price">￥{{ product.goodsTotalPrice }}</text>
              </view>

              <!-- 购物车按钮 -->
              <!-- <view class="cart-btn" @click.stop="addToCart">
							<uni-icons type="cart" size="30" color="#ff5490"></uni-icons>
						</view> -->
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 价格排序 -->
      <scroll-view scroll-y class="content-scroll" v-if="activeTab === 1">
        <view class="product-section" v-for="(product, index) in sortedProducts" :key="index"
          @click="goToDetails(product.id)">
          <!-- 商品图片 -->
          <image class="product-image" :src="getFirstImage(product.goodsCover)" mode="widthFix" />

          <!-- 商品信息 -->
          <view class="product-info">
            <text class="product-title">{{ product.goodsName }}</text>

            <view class="product-bought">
              <!-- 价格标签 -->
              <view class="price-tag">
                <text class="price">￥{{ product.goodsTotalPrice }}</text>
              </view>

              <!-- 购物车按钮 -->
              <!-- <view class="cart-btn" @click.stop="addToCart">
							<uni-icons type="cart" size="30" color="#ff5490"></uni-icons>
						</view> -->
            </view>
          </view>
        </view>
      </scroll-view>
    </view>


  </view>
</template>

<script>
import {
  getGoodsList,
  queryGoodsByName,
  getAllGoodsList,
  getGoodsDetail,
} from "@/api/goods";

export default {
  data() {
    return {
      activeTab: 0, // 默认显示第一个标签
      tabs: [
        {
          title: "综合",
        },
        {
          title: "价格",
        },
      ],
      activeArrow: "up", // 记录当前激活的箭头
      sortOrder: "asc", // 默认升序排序
      clickCount: 0, // 点击次数
      searchKeyword: "", // 搜索关键词
      // 添加默认测试数据，如果接口正常将被覆盖
      products: []
    };
  },
  onLoad() {
    // 直接加载商品数据，无需权限检查
    this.loadProducts();
  },
  methods: {


    // 获取第一张图片作为封面
    getFirstImage(imageStr) {
      if (!imageStr) return "";
      // 将逗号分隔的图片字符串拆分，返回第一张图片URL
      const images = imageStr.split(",");
      return images[0] || "";
    },
    // 加载商品数据
    loadProducts() {
      uni.showLoading({
        title: "加载中...",
      });

      // 根据当前激活的标签选择不同的处理逻辑
      if (this.activeTab === 0) {
        // 综合接口不传参数
        let data = {
          logicalDel: 0,
        };
        getGoodsDetail(data)
          .then((res) => {
            uni.hideLoading();
            console.log("综合接口返回数据:", res);

            if (res.data) {
              // 综合接口直接返回数组
              if (Array.isArray(res.data)) {
                this.products = res.data;
                console.log("处理后的商品列表:", this.products);
              } else {
                this.products = [];
                console.error("综合接口返回数据格式不正确");
                uni.showToast({
                  title: "数据格式错误",
                  icon: "none",
                });
              }
            } else {
              uni.showToast({
                title: "数据加载失败",
                icon: "none",
              });
            }
          })
          .catch(this.handleError);
      } else {
        // 价格接口需要传sortOrder参数
        const params = {
          sortOrder: this.sortOrder,
        };
        getGoodsList(params)
          .then((res) => {
            uni.hideLoading();
            console.log("价格接口返回数据:", res);

            if (res.data && res.data.code === 200) {
              // 价格接口返回嵌套结构
              if (res.data.data && Array.isArray(res.data.data)) {
                this.products = res.data.data;
                console.log("处理后的商品列表:", this.products);
              } else {
                this.products = [];
                console.error("接口返回数据格式不正确");
                uni.showToast({
                  title: "数据格式错误",
                  icon: "none",
                });
              }
            } else {
              uni.showToast({
                title: "数据加载失败",
                icon: "none",
              });
            }
          })
          .catch(this.handleError);
      }
    },

    // 处理接口错误
    handleError(err) {
      console.error("请求失败:", err);
      uni.hideLoading();
      uni.showToast({
        title: "网络连接失败",
        icon: "none",
      });
    },
    // 切换标签
    switchTab(index) {
      this.activeTab = index;
      // 切换标签后重新加载数据
      this.loadProducts();
    },
    // 去商品详情
    goToDetails(id) {
      uni.navigateTo({
        url: `/subpackages/goods/pages/goodsfilter/goodsDetails?id=${id}`,
      });
    },
    // 去搜索页面
    goToSearch() {
      console.log("聚焦搜索框");
    },
    // 搜索确认
    search(e) {
      if (!e.value || e.value.trim() === "") {
        return;
      }

      uni.showLoading({
        title: "搜索中...",
      });

      // 调用queryGoodsByName方法搜索商品
      queryGoodsByName({
        goodsName: e.value,
      })
        .then((res) => {
          uni.hideLoading();
          console.log("搜索结果:", res);

          if (res.data && res.data.code === 200) {
            // 根据API返回结构获取数据
            if (res.data.data && Array.isArray(res.data.data)) {
              this.products = res.data.data;
            } else if (
              res.data.data &&
              res.data.data.list &&
              Array.isArray(res.data.data.list)
            ) {
              this.products = res.data.data.list;
            } else {
              this.products = [];
              uni.showToast({
                title: "未找到相关商品",
                icon: "none",
              });
            }
          } else {
            uni.showToast({
              title: "搜索失败",
              icon: "none",
            });
          }
        })
        .catch((err) => {
          console.error("搜索请求失败:", err);
          uni.hideLoading();
          uni.showToast({
            title: "网络连接失败",
            icon: "none",
          });
        });
    },
    // 添加到购物车
    addToCart() {
      uni.showToast({
        title: "已加入购物车",
        icon: "success",
      });
    },
    // 箭头点击处理
    handleArrowClick(direction) {
      // this.activeArrow = direction

      // 修改排序逻辑：在asc和desc之间交替
      if (this.sortOrder === "asc") {
        this.sortOrder = "desc";
      } else {
        this.sortOrder = "asc";
      }

      // 重新加载数据
      // this.loadProducts()
    },
    // 处理搜索按钮点击
    handleSearch() {
      if (!this.searchKeyword || this.searchKeyword.trim() === "") {
        // 查所有
        this.loadProducts();
        return;
      }

      // 调用搜索方法
      this.search({
        value: this.searchKeyword,
      });
    },
  },
  computed: {
    // 计算属性：处理价格排序
    sortedProducts() {
      return Array.isArray(this.products) ? this.products : [];
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  height: 100vh !important;
  width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  background: #f5f5f5 !important;
  box-sizing: border-box !important;
}

.content {
  width: 100% !important;
  box-sizing: border-box !important;
}

.header {
  background: #fff;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 212, 170, 0.1);
  z-index: 10;
}

.search-container {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  :deep(.uni-searchbar) {
    flex: 1;

    .uni-searchbar__box {
      background: #f5f5f5 !important;
      border-radius: 25px !important;
      border: 2px solid transparent !important;
      transition: all 0.3s ease !important;

      &:focus-within {
        border-color: #00d4aa !important;
        background: #fff !important;
      }
    }

    .uni-searchbar__text-input {
      font-size: 15px !important;
      color: #333 !important;
    }
  }
}

.tabs {
  display: flex;
  border-bottom: 2px solid #f0f0f0;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 15px 20px;
  font-size: 16px;
  color: #666;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;

  &.active {
    color: #00d4aa;
    font-weight: 600;

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 30px;
      height: 3px;
      background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
      border-radius: 2px;
    }
  }
}

.content-scroll {
  flex: 1 !important;
  overflow: hidden !important;
  padding: 12px !important;
  box-sizing: border-box !important;
}

.product-section {
  display: flex !important;
  align-items: flex-start !important;
  padding: 12px !important;
  margin-bottom: 10px !important;
  border-radius: 12px !important;
  background: #fff !important;
  box-shadow: 0 2px 8px rgba(0, 212, 170, 0.06) !important;
  transition: all 0.3s ease !important;
  width: 100% !important;
  box-sizing: border-box !important;
  overflow: hidden !important;

  &:active {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 15px rgba(0, 212, 170, 0.12) !important;
  }
}

.product-image {
  width: 70px !important;
  height: 70px !important;
  margin-right: 10px !important;
  border-radius: 8px !important;
  flex-shrink: 0 !important;
  border: 1px solid #f0f0f0 !important;
}

.product-info {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: space-between !important;
  min-width: 0 !important;
  max-width: calc(100% - 80px) !important;
  overflow: hidden !important;
}

.product-title {
  font-size: 13px !important;
  font-weight: 500 !important;
  color: #333 !important;
  margin-bottom: 6px !important;
  line-height: 1.3 !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  -webkit-box-orient: vertical !important;
  word-break: break-all !important;
  max-height: 34px !important;
}

.product-bought {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  margin-top: auto !important;
}

.price-tag {
  display: flex !important;
  align-items: baseline !important;
}

.price {
  font-size: 15px !important;
  color: #00d4aa !important;
  font-weight: 600 !important;
  letter-spacing: 0.2px !important;
  white-space: nowrap !important;
}

.cart-btn {
  padding: 4px !important;
  border-radius: 6px !important;
  transition: all 0.3s ease !important;
  margin-left: 6px !important;

  &:active {
    background: rgba(0, 212, 170, 0.1) !important;
    transform: scale(0.95) !important;
  }
}

.arrow-container {
  display: flex;
  flex-direction: column;
  margin-left: 10px;
}

.arrow {
  font-size: 12px;
  color: #999;
  line-height: 1.5;
  transition: all 0.3s ease;

  &.active {
    color: #00d4aa;
  }

  &:active {
    opacity: 0.7;
  }
}

/* Custom search bar styles */
.custom-search-bar {
  flex: 1;
  margin-right: 15px;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 18px;
  padding: 0 12px;
  height: 36px;
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
}

.search-input-wrapper:focus-within {
  border-color: #00d4aa;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 212, 170, 0.1);
}

.search-icon {
  font-size: 14px;
  margin-right: 8px;
  color: #999;
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  outline: none;
  font-size: 13px;
  color: #333;
  height: 100%;
}

.search-input::placeholder {
  color: #999;
}

.search-button {
  flex-shrink: 0;
}

.search-button button {
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  color: white;
  border: none;
  border-radius: 18px;
  width: 60px;
  height: 36px;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.3s ease;
  letter-spacing: 0.3px;
  display: flex;
  justify-self: center;
  align-items: center;
}

.search-button button:active {
  transform: scale(0.95);
}
</style>
