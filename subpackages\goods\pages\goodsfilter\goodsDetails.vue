<template>
  <view class="goods-detail-container">
    <view class="container" v-if="product">
      <!-- 商品封面区域 -->
      <view class="product-gallery">
        <swiper class="main-image-swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500"
          circular>
          <swiper-item v-for="(img, index) in coverImages" :key="index">
            <image class="main-image" :src="img" mode="aspectFill" @click="previewCoverImage(img, index)" />
          </swiper-item>
        </swiper>
      </view>

      <!-- 商品信息卡片 -->
      <view class="product-info-card">
        <!-- 价格区域 - 突出显示 -->
        <view class="price-section">
          <view class="price-highlight">
            <text class="price-symbol">￥</text>
            <text class="price-amount">{{ product.goodsTotalPrice }}</text>
          </view>
        </view>

        <!-- 商品标题 -->
        <view class="title-section">
          <text class="product-title">{{ product.goodsName }}</text>
        </view>

        <!-- 服务保障 -->
        <view class="service-section" @click="showServe()">
          <text class="service-label">服务</text>
          <text class="service-text single-line-ellipsis">{{
            product.serviceDescription
          }}</text>
          <text class="service-arrow">›</text>
        </view>
      </view>

      <!-- 详情导航区域 -->
      <view class="detail-section">
        <!-- 导航标签栏 -->
        <view class="nav-tabs">
          <view v-for="(tab, index) in tabs" :key="index" class="tab-item" :class="{ active: activeTab === index }"
            @click="switchTab(index)">
            {{ tab.title }}
          </view>
        </view>

        <!-- 导航内容栏 -->
        <view class="nav-content">
          <view v-if="activeTab === 0">
            <view v-if="detailImages && detailImages.length > 0">
              <image v-for="(img, index) in detailImages" :key="index" :src="img" class="detail-image" mode="widthFix"
                @click="previewImage(img, index)"></image>
            </view>
            <view v-else class="no-content">暂无图文详情</view>
          </view>
          <view v-else class="parameter-content">
            <text class="parameter-text">{{ product.goodsParameter }}</text>
          </view>
        </view>
      </view>

      <!-- 底部操作栏 -->
      <view class="footer">
        <view class="action-btn buy-btn" @click="buyNow">
          <text class="btn-text">立即购买</text>
        </view>
      </view>
    </view>
    <view v-else class="loading">
      <text>加载中...</text>
    </view>

    <uni-popup ref="popup" background-color="#fff" border-radius="10px" @change="change">
      <view class="popup-content">
        <text class="text">{{ product.serviceDescription }}</text>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { getGoodsDetail } from "@/api/goods";
import { isLogin } from "@/utils/auth.js";

export default {
  data() {
    return {
      activeTab: 0,
      tabs: [
        {
          title: "图文详情",
        },
        {
          title: "商品参数",
        },
      ],
      product: null,
      goodsId: 0,
      coverImages: [], // 存储分割后的封面图片
      detailImages: [], // 存储分割后的详情图片
    };
  },
  onLoad(options) {
    // 获取传递的商品ID
    if (options.id) {
      this.goodsId = options.id;
      this.loadGoodsDetail();
    } else {
      uni.showToast({
        title: "商品信息获取失败",
        icon: "none",
      });
    }
  },
  methods: {
    // 加载商品详情
    loadGoodsDetail() {
      uni.showLoading({
        title: "加载中...",
      });

      getGoodsDetail({
        id: this.goodsId,
      })
        .then((res) => {
          uni.hideLoading();
          console.log("商品详情:", res);

          if (res.data) {
            // 处理返回的数组数据，取第一个元素
            if (Array.isArray(res.data) && res.data.length > 0) {
              this.product = res.data[0]; // 从数组中取第一个商品
              // 处理商品封面图片
              this.handleCoverImages();
              // 处理商品详情图片
              this.handleDetailImages();
              console.log("处理后的商品数据:", this.product);
            } else {
              uni.showToast({
                title: "商品不存在",
                icon: "none",
              });
            }
          } else {
            uni.showToast({
              title: "数据加载失败",
              icon: "none",
            });
          }
        })
        .catch((err) => {
          console.error("请求失败:", err);
          uni.hideLoading();
          uni.showToast({
            title: "网络连接失败",
            icon: "none",
          });
        });
    },
    // 处理商品封面图片
    handleCoverImages() {
      if (this.product && this.product.goodsCover) {
        this.coverImages = this.product.goodsCover
          .split(",")
          .filter((img) => img.trim() !== "");
        console.log("处理后的封面图片:", this.coverImages);
      } else {
        this.coverImages = [];
      }
    },
    // 处理商品详情图片
    handleDetailImages() {
      if (this.product && this.product.goodsPic) {
        this.detailImages = this.product.goodsPic
          .split(",")
          .filter((img) => img.trim() !== "");
        console.log("处理后的详情图片:", this.detailImages);
      } else {
        this.detailImages = [];
      }
    },
    switchTab(index) {
      this.activeTab = index;
    },
    buyNow() {
      console.log("立即购买");

      // 检查登录状态
      if (!isLogin()) {
        uni.showModal({
          title: "提示",
          content: "请先登录后再购买商品",
          showCancel: true,
          cancelText: "取消",
          confirmText: "去登录",
          success: (res) => {
            if (res.confirm) {
              // 跳转到登录页面
              uni.navigateTo({
                url: "/pages/unifiedLogin/unifiedLogin",
              });
            }
          },
        });
        return;
      }

      // 已登录，跳转到购买页面
      uni.navigateTo({
        url: `/subpackages/shop/pages/selfOrderBuy/selfOrderBuy?id=${this.goodsId}`,
      });
    },
    // 去首页
    goToHome() {
      uni.navigateTo({
        url: "/pages/my/my",
      });
    },
    showServe() {
      this.$refs.popup.open("bottom");
    },
    change(e) {
      // uni-popup 组件的 change 事件处理
      console.log("popup change:", e);
    },
    // 预览图片
    previewImage(currentImg, index) {
      uni.previewImage({
        current: index,
        urls: this.detailImages,
        indicator: "number",
        longPressActions: {
          itemList: ["保存图片"],
          success: function (data) {
            console.log("选中了第" + (data.tapIndex + 1) + "个按钮");
          },
          fail: function (err) {
            console.log(err.errMsg);
          },
        },
      });
    },
    // 预览封面图片
    previewCoverImage(currentImg, index) {
      uni.previewImage({
        current: index,
        urls: this.coverImages,
        indicator: "number",
        longPressActions: {
          itemList: ["保存图片"],
          success: function (data) {
            console.log("选中了第" + (data.tapIndex + 1) + "个按钮");
          },
          fail: function (err) {
            console.log(err.errMsg);
          },
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.goods-detail-container {
  min-height: 100vh;
  background: #f8f9fa;
}

.container {
  background: #f8f9fa;
  padding-bottom: 120px;
}

/* 商品图片展示区 */
.product-gallery {
  position: relative;
  background: #fff;
  margin-bottom: 12px;
}

.main-image-swiper {
  width: 100%;
  height: 400px;
  background: #fff;
}

.main-image {
  width: 100%;
  height: 400px;
  object-fit: cover;
}

/* 商品信息卡片 - 电商风格 */
.product-info-card {
  background: #fff;
  margin: 0 0 12px 0;
  padding: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 价格区域 - 突出显示 */
.price-section {
  padding: 20px 20px 15px 20px;
  background: linear-gradient(135deg, #fff5f5 0%, #fff 100%);
  border-bottom: 1px solid #f0f0f0;
}

.price-highlight {
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.price-symbol {
  font-size: 20px;
  color: #00d4aa;
  font-weight: 600;
  line-height: 1;
}

.price-amount {
  font-size: 36px;
  color: #00d4aa;
  font-weight: 700;
  line-height: 1;
  letter-spacing: -1px;
}

/* 商品标题区域 */
.title-section {
  padding: 18px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.product-title {
  font-size: 18px;
  color: #333;
  font-weight: 600;
  line-height: 1.4;
  letter-spacing: 0.2px;
}

/* 服务保障区域 */
.service-section {
  padding: 15px 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;

  &:active {
    background: #f8f9fa;
  }
}

.service-label {
  font-size: 14px;
  color: #00d4aa;
  font-weight: 600;
  flex-shrink: 0;
}

.service-text {
  font-size: 14px;
  color: #666;
  flex: 1;
  line-height: 1.4;
}

/* 详情导航区域 */
.detail-section {
  background: #fff;
  margin: 0 0 12px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 导航标签样式 */
.nav-tabs {
  display: flex;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16px 0;
  font-size: 16px;
  color: #666;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;

  &.active {
    color: #00d4aa;
    font-weight: 600;

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 40px;
      height: 3px;
      background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
      border-radius: 2px;
    }
  }
}

/* 导航内容样式 */
.nav-content {
  background: #fff;
  padding: 20px;
  min-height: 200px;
}

.detail-image {
  width: 100%;
  margin-bottom: 8px;
  border-radius: 6px;
}

.parameter-content {
  width: 100%;
}

.parameter-text {
  font-size: 14px;
  color: #333;
  line-height: 1.6;
}

.no-content {
  padding: 60px 20px;
  color: #999;
  font-size: 14px;
  text-align: center;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 15px;
  color: #999;
}

/* 底部操作栏 - 电商风格 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 15px 20px;
  padding-bottom: calc(15px + env(safe-area-inset-bottom));
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 100;
  border-top: 1px solid #f0f0f0;
}

.action-btn {
  width: 100%;
  height: 54px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 27px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:active {
    transform: scale(0.98);
  }
}

.buy-btn {
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.4);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent);
    transition: left 0.6s;
  }

  &:active::before {
    left: 100%;
  }
}

.btn-text {
  color: white;
  font-size: 18px;
  font-weight: 700;
  letter-spacing: 1px;
}

/* 工具样式 */
.single-line-ellipsis {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

/* 弹窗样式 */
.popup-content {
  height: 40vh;
  padding: 30px;
  border-radius: 20px 20px 0 0;

  .text {
    font-size: 14px;
    color: #333;
    line-height: 1.8;
    letter-spacing: 0.3px;
  }
}
</style>
